import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_mesh/gen/assets.gen.dart';
import 'package:toii_mesh/model/user/user_model.dart';
import 'package:toii_mesh/screen/chats/widgets/user_info_widget.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';
import 'package:toii_mesh/widget/text_field.dart/text_field.dart';

class MyProfileScreen extends StatefulWidget {
  const MyProfileScreen({super.key});

  @override
  State<MyProfileScreen> createState() => _MyProfileScreenState();
}

class _MyProfileScreenState extends State<MyProfileScreen> {
  late TextEditingController _displayNameController;
  late TextEditingController _usernameController;
  late FocusNode _displayNameFocusNode;
  late FocusNode _usernameFocusNode;

  @override
  void initState() {
    super.initState();
    _displayNameController = TextEditingController();
    _usernameController = TextEditingController();
    _displayNameFocusNode = FocusNode();
    _usernameFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _usernameController.dispose();
    _displayNameFocusNode.dispose();
    _usernameFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<ProfileCubit>()..getProfile(),
      child: Scaffold(
        backgroundColor: const Color(0xFFFFFFFF), // Neutrals/Neutral 50 [day]
        body: BlocBuilder<ProfileCubit, ProfileState>(
          builder: (context, state) {
            final user = state.userModel;
            final displayName = user?.username ?? 'Robert Fox123';
            final username = user?.username ?? '@meshii235';

            // Set initial values if not already set
            if (_displayNameController.text.isEmpty) {
              _displayNameController.text = displayName;
            }
            if (_usernameController.text.isEmpty) {
              _usernameController.text = username;
            }

            return SafeArea(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: Row(
                        children: [
                          // Back button
                          GestureDetector(
                            onTap: () => context.pop(),
                            child: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: context.themeData.black50,
                                borderRadius: BorderRadius.circular(30),
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.arrow_back_ios,
                                  size: 14,
                                  color: context.themeData.neutral800,
                                ),
                              ),
                            ),
                          ),
                          const Spacer(),
                        ],
                      ),
                    ),
                    const SizedBox(height: 102),

                    // Profile header section
                    _buildProfileHeader(displayName, user),

                    const SizedBox(height: 24),

                    // XMTP User Info Widget
                    const UserInfoWidget(),

                    const SizedBox(height: 24),

                    // Form fields
                    _buildFormFields(),

                    const SizedBox(height: 100),

                    // Update Profile button
                    _buildUpdateButton(),

                    // const SizedBox(height: 26),

                    // // Delete account option
                    // _buildDeleteAccountOption(),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileHeader(String displayName, UserModel? user) {
    return Column(
      children: [
        // Avatar with camera icon
        Stack(
          children: [
            // Avatar
            (user?.avatarUrl ?? "").isNotEmpty
                ? Container(
                    width: 104,
                    height: 104,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(52),
                      image: DecorationImage(
                        image: AssetImage(Assets.images.avatarSample.path),
                        fit: BoxFit.cover,
                      ),
                    ),
                  )
                : ClipRRect(
                    borderRadius: BorderRadius.circular(50),
                    child: Container(
                      width: 104,
                      height: 104,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(52),
                        image: DecorationImage(
                          image: AssetImage(
                              Assets.images.avatarKristinWatson.path),
                          fit: BoxFit.cover,
                        ),
                      ),
                    )),
            // Camera icon
            Positioned(
              right: 4,
              bottom: 3,
              child: Container(
                width: 22,
                height: 22,
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C4EFF), // Primary/blue_500
                  borderRadius: BorderRadius.circular(100),
                ),
                child: const Icon(
                  Icons.camera_alt,
                  size: 14,
                  color: Color(0xE6FFFFFF), // White transperant/White-900
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // User name
        Text(
          displayName,
          style: titleLarge.copyWith(
            color: const Color(0xFF292929), // Neutrals/Neutral 800 [day]
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildFormFields() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Display name field
          TTextField(
            labelText: 'Displayname',
            textController: _displayNameController,
            focusNode: _displayNameFocusNode,
            fillColor: Colors.white,
            textStyle: bodyLarge.copyWith(
              color: const Color(0xFF292929), // Neutrals/Neutral 800 [day]
            ),
          ),

          const SizedBox(height: 16),

          // Username field
          TTextField(
            labelText: 'Username',
            textController: _usernameController,
            focusNode: _usernameFocusNode,
            fillColor: Colors.white,
            textStyle: bodyLarge.copyWith(
              color: const Color(0xFF292929), // Neutrals/Neutral 800 [day]
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        width: 358,
        height: 48,
        decoration: BoxDecoration(
          color: const Color(0xFFF6F6F6), // Neutrals/Neutral 100 [day]
          borderRadius: BorderRadius.circular(48),
        ),
        child: Center(
          child: Text(
            'Update Profile',
            style: titleMedium.copyWith(
              color: const Color(0xFFAFAFAF), // Neutrals/Neutral 300 [day]
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDeleteAccountOption() {
    return Container(
      width: 390,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          // Close circle icon
          const SizedBox(
            width: 24,
            height: 24,
            child: Icon(
              Icons.cancel_outlined,
              size: 20,
              color: Color(0xFFD33636), // Foundation/Red/Red-500
            ),
          ),

          const SizedBox(width: 16),

          // Delete account text
          Expanded(
            child: Text(
              'Delete account',
              style: titleMedium.copyWith(
                color: const Color(0xFFD33636), // Foundation/Red/Red-600
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          // Arrow right icon
          const SizedBox(
            width: 24,
            height: 24,
            child: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(0xFFD33636), // Foundation/Red/Red-500
            ),
          ),
        ],
      ),
    );
  }
}
