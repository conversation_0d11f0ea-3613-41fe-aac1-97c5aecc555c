import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/gen/assets.gen.dart';
import 'package:toii_mesh/model/user/user_model.dart';
import 'package:toii_mesh/screen/tabs/people_tab_screen.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';

class UserProfileScreen extends StatefulWidget {
  final UserModel? user;
  final FigmaPerson? figmaPerson;

  const UserProfileScreen({
    super.key,
    this.user,
    this.figmaPerson,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  late String userName;
  late String userAvatar;
  late String userId;

  @override
  void initState() {
    super.initState();
    // Handle both UserModel and FigmaPerson data
    if (widget.user != null) {
      userName = widget.user!.fullName ?? widget.user!.username;
      userAvatar = widget.user!.avatarUrl ?? Assets.images.avatarSample.path;
      userId = widget.user!.id;
    } else if (widget.figmaPerson != null) {
      userName = widget.figmaPerson!.name;
      userAvatar = widget.figmaPerson!.avatarPath;
      userId =
          'figma_${widget.figmaPerson!.name.replaceAll(' ', '_').toLowerCase()}';
    } else {
      userName = 'Unknown User';
      userAvatar = Assets.images.avatarSample.path;
      userId = 'unknown';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.themeData.neutral100,
      body: Column(
        children: [
          // Status bar space
          Container(
            height: MediaQuery.of(context).padding.top,
            color: context.themeData.neutral100,
          ),

          // Header with back button
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // Back button
                GestureDetector(
                  onTap: () => context.pop(),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: context.themeData.black50,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.arrow_back_ios,
                        size: 14,
                        color: context.themeData.neutral800,
                      ),
                    ),
                  ),
                ),
                const Spacer(),
              ],
            ),
          ),

          // Main content
          Expanded(
            child: Container(
              color: context.themeData.neutral100,
              child: Column(
                children: [
                  const SizedBox(height: 16),

                  // Profile header section
                  _buildProfileHeader(),

                  const SizedBox(height: 24),

                  // Action buttons
                  _buildActionButtons(),

                  const SizedBox(height: 16),

                  // Settings sections
                  Expanded(
                    child: _buildSettingsSections(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Column(
      children: [
        // Avatar with online indicator
        Stack(
          children: [
            Container(
              width: 104,
              height: 104,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(52),
                image: DecorationImage(
                  image: AssetImage(userAvatar),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            // Online indicator (optional)
            Positioned(
              right: 4,
              bottom: 4,
              child: Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: context.themeData.primaryBlue500,
                  borderRadius: BorderRadius.circular(5),
                  border: Border.all(
                    color: Colors.white,
                    width: 3,
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 6),

        // User name
        Text(
          userName,
          style: titleLarge.copyWith(
            color: context.themeData.neutral800,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Add friend button
          Expanded(
            child: _buildActionButton(
              'Add friend',
              Assets.icons.icProfileAdd.svg(
                width: 20,
                height: 20,
                colorFilter: ColorFilter.mode(
                  context.themeData.neutral500,
                  BlendMode.srcIn,
                ),
              ),
              context.themeData.neutral50,
              () {
                // TODO: Add friend functionality
              },
            ),
          ),

          const SizedBox(width: 12),

          // Chat button
          Expanded(
            child: _buildActionButton(
              'Chat',
              Assets.icons.icChatSvg.svg(
                width: 20,
                height: 20,
                colorFilter: ColorFilter.mode(
                  context.themeData.neutral500,
                  BlendMode.srcIn,
                ),
              ),
              context.themeData.neutral50,
              () {
                // TODO: Chat functionality
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String title,
    Widget icon,
    Color backgroundColor,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(32),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon,
            const SizedBox(width: 10),
            Text(
              title,
              style: titleMedium.copyWith(
                color: context.themeData.neutral500,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsSections() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // First settings group
          Container(
            decoration: BoxDecoration(
              color: context.themeData.neutral50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                _buildSettingsItem(
                  'Nickname',
                  Assets.icons.icNickName.svg(
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                      context.themeData.primaryBlue500,
                      BlendMode.srcIn,
                    ),
                  ),
                  isFirst: true,
                  onTap: () {
                    // TODO: Edit nickname
                  },
                ),
                _buildDivider(),
                _buildSettingsItem(
                  'Groups in Common',
                  Assets.icons.icProfile2user.svg(
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                      context.themeData.primaryBlue500,
                      BlendMode.srcIn,
                    ),
                  ),
                  isLast: true,
                  onTap: () {
                    // TODO: Show groups in common
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Second settings group
          Container(
            decoration: BoxDecoration(
              color: context.themeData.neutral50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                _buildSettingsItem(
                  'Unblock',
                  Assets.icons.icBlockSvg.svg(
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                      context.themeData.primaryBlue500,
                      BlendMode.srcIn,
                    ),
                  ),
                  isFirst: true,
                  onTap: () {
                    // TODO: Unblock user
                  },
                ),
                _buildDivider(),
                _buildSettingsItem(
                  'Report',
                  Assets.icons.icNickName.svg(
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                      context.themeData.primaryBlue500,
                      BlendMode.srcIn,
                    ),
                  ),
                  isLast: true,
                  onTap: () {
                    // TODO: Report user
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Suggestions section
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Suggestions',
                  style: titleLarge.copyWith(
                    color: context.themeData.neutral400,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildSuggestionsList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsItem(
    String title,
    Widget icon, {
    bool isFirst = false,
    bool isLast = false,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        child: Row(
          children: [
            // Icon
            SizedBox(
              width: 24,
              height: 24,
              child: icon,
            ),

            const SizedBox(width: 12),

            // Title
            Expanded(
              child: Text(
                title,
                style: bodyLarge.copyWith(
                  color: context.themeData.neutral800,
                ),
              ),
            ),

            // Arrow
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: context.themeData.neutral300,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.only(left: 48),
      height: 1,
      color: context.themeData.neutral100,
    );
  }

  Widget _buildSuggestionsList() {
    // Sample suggestions data
    final suggestions = [
      FigmaPerson(
        name: 'Kristin Watson',
        avatarPath: Assets.images.avatarKristinWatson.path,
        buttonText: 'Add friend',
        buttonType: 'primary',
      ),
      FigmaPerson(
        name: 'Albert Flores',
        avatarPath: Assets.images.avatarAlbertFlores.path,
        buttonText: 'Add friend',
        buttonType: 'primary',
      ),
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: suggestions.map((person) {
          return Container(
            width: 200,
            height: 200,
            margin: const EdgeInsets.only(right: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: context.themeData.neutral50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // Avatar
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(28),
                    image: DecorationImage(
                      image: AssetImage(person.avatarPath),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // Person info
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      person.name,
                      style: titleMedium.copyWith(
                        color: context.themeData.neutral800,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      'Username',
                      style: bodyLarge.copyWith(
                        color: context.themeData.neutral400,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Add friend button
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: context.themeData.primaryBlue500,
                    borderRadius: BorderRadius.circular(48),
                  ),
                  child: Text(
                    'Add friend',
                    style: titleMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}
