import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/cubit/auth/create_account/create_account_cubit.dart';
import 'package:toii_mesh/gen/assets.gen.dart';
import 'package:toii_mesh/router/app_router.dart';
import 'package:toii_mesh/widget/snack_bar/snackbar.dart';

class CreateAccountScreen extends StatefulWidget {
  final String? address;
  const CreateAccountScreen({super.key, this.address});

  @override
  State<CreateAccountScreen> createState() => _CreateAccountScreenState();
}

class _CreateAccountScreenState extends State<CreateAccountScreen> {
  @override
  void initState() {
    super.initState();
    // initPlatformState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          CreateAccountCubit()..createAccountNoPasskey(widget.address),
      child: BlocConsumer<CreateAccountCubit, CreateAccountState>(
        listener: (context, state) async {
          if (state.status.isFailure) {
            context.showSnackbar(message: state.errorMessage ?? "");
            // Navigator.of(context).pop(state.errorMessage);
          }
          if (state.status.isSuccess) {
            context.go(RouterEnums.inital.routeName);
          }
        },
        builder: (context, state) {
          return Stack(
            children: [
              SizedBox(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                child: Assets.images.registerLoading.image(
                  fit: BoxFit.cover,
                ),
              ),
              // Lottie.asset(
              //   Assets.json.processing,

              //   ///     'assets/LottieLogo1.json',
              //   //  controller: _controller,
              //   // onLoaded: (composition) {
              //   //   // Configure the AnimationController with the duration of the
              //   //   // Lottie file and start the animation.
              //   //   _controller
              //   //     ..duration = composition.duration
              //   //     ..forward();
              //   // },
              // ),
            ],
          );
        },
      ),
    );
  }
}
