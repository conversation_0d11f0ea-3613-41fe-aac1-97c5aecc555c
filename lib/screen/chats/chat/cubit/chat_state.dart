import 'package:equatable/equatable.dart';
import 'package:toii_mesh/model/chat/chat_models.dart';

/// Chat state for managing individual chat screen state
abstract class ChatState extends Equatable {
  const ChatState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class ChatInitial extends ChatState {
  const ChatInitial();
}

/// Loading messages state
class ChatLoading extends ChatState {
  const ChatLoading();
}

/// Messages loaded successfully
class ChatLoaded extends ChatState {
  final ChatConversation conversation;
  final List<ChatMessage> messages;
  final bool isStreaming;

  const ChatLoaded({
    required this.conversation,
    required this.messages,
    this.isStreaming = false,
  });

  ChatLoaded copyWith({
    ChatConversation? conversation,
    List<ChatMessage>? messages,
    bool? isStreaming,
  }) {
    return ChatLoaded(
      conversation: conversation ?? this.conversation,
      messages: messages ?? this.messages,
      isStreaming: isStreaming ?? this.isStreaming,
    );
  }

  @override
  List<Object?> get props => [conversation, messages, isStreaming];
}

/// Sending message state
class ChatSendingMessage extends ChatLoaded {
  final String pendingMessageId;

  const ChatSendingMessage({
    required super.conversation,
    required super.messages,
    required this.pendingMessageId,
    super.isStreaming,
  });

  @override
  List<Object?> get props => [...super.props, pendingMessageId];
}

/// Message sent successfully
class ChatMessageSent extends ChatLoaded {
  final String sentMessageId;

  const ChatMessageSent({
    required super.conversation,
    required super.messages,
    required this.sentMessageId,
    super.isStreaming,
  });

  @override
  List<Object?> get props => [...super.props, sentMessageId];
}

/// New message received via streaming
class ChatMessageReceived extends ChatLoaded {
  final ChatMessage newMessage;

  const ChatMessageReceived({
    required super.conversation,
    required super.messages,
    required this.newMessage,
    super.isStreaming,
  });

  @override
  List<Object?> get props => [...super.props, newMessage];
}

/// Error state
class ChatError extends ChatState {
  final String message;
  final String? conversationId;

  const ChatError({
    required this.message,
    this.conversationId,
  });

  @override
  List<Object?> get props => [message, conversationId];
}
