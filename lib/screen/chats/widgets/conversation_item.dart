import 'package:flutter/material.dart';
import 'package:toii_mesh/model/chat/chat_models.dart';

/// Individual conversation item widget
class ConversationItem extends StatelessWidget {
  final ChatConversation conversation;
  final VoidCallback? onTap;

  const ConversationItem({
    super.key,
    required this.conversation,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final String title = _getDisplayTitle();
    final String subtitle = _getDisplaySubtitle();
    final Color avatarColor = _getAvatarColor();
    final String avatarText = _getAvatarText();

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Avatar
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: avatarColor,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Center(
                    child: Text(
                      avatarText,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Conversation content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF292929),
                              ),
                            ),
                          ),
                          if (conversation.lastMessageTime != null)
                            Text(
                              _formatTime(conversation.lastMessageTime!),
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: Color(0xFFAFAFAF), // Neutral 300
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              subtitle,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: conversation.unreadCount > 0 
                                    ? FontWeight.w500 
                                    : FontWeight.w400,
                                color: conversation.unreadCount > 0
                                    ? const Color(0xFF292929) // Neutral 800
                                    : const Color(0xFF777777), // Neutral 400
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (conversation.unreadCount > 0)
                            Container(
                              width: 10,
                              height: 10,
                              decoration: const BoxDecoration(
                                color: Color(0xFF6C4EFF), // Primary blue
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Status indicator
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: conversation.isActive ? Colors.green : Colors.grey,
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getDisplayTitle() {
    if (conversation.title.isNotEmpty) {
      return conversation.title;
    }

    // Fallback title generation
    if (conversation.peerInboxId != null) {
      final peerIdDisplay = conversation.peerInboxId!.length >= 8
          ? conversation.peerInboxId!.substring(0, 8)
          : conversation.peerInboxId!;
      return 'DM with $peerIdDisplay...';
    }

    final conversationIdDisplay = conversation.id.length >= 8
        ? conversation.id.substring(0, 8)
        : conversation.id;
    return 'Chat $conversationIdDisplay...';
  }

  String _getDisplaySubtitle() {
    if (conversation.lastMessageContent != null && 
        conversation.lastMessageContent!.isNotEmpty) {
      return conversation.lastMessageContent!;
    }
    
    return conversation.type.toString().contains('dm') 
        ? 'Direct message' 
        : 'Group conversation';
  }

  Color _getAvatarColor() {
    // Generate color based on conversation ID
    final hash = conversation.id.hashCode;
    final colors = [
      const Color(0xFF6C4EFF),
      Colors.blue,
      Colors.green,
      Colors.purple,
      Colors.orange,
      const Color(0xFFD33636),
    ];
    return colors[hash.abs() % colors.length];
  }

  String _getAvatarText() {
    if (conversation.peerInboxId != null) {
      return conversation.peerInboxId!.substring(0, 1).toUpperCase();
    }
    
    final conversationId = conversation.id;
    return conversationId.isNotEmpty 
        ? conversationId.substring(0, 1).toUpperCase()
        : 'C';
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}
