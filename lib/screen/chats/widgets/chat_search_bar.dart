import 'package:flutter/material.dart';

/// Search bar widget for the chats tab
class ChatSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback? onAddPressed;
  final VoidCallback? onQrPressed;
  final VoidCallback? onReloadPressed;
  final ValueChanged<String>? onSearchChanged;
  final bool isReloading;

  const ChatSearchBar({
    super.key,
    required this.controller,
    this.onAddPressed,
    this.onQrPressed,
    this.onReloadPressed,
    this.onSearchChanged,
    this.isReloading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          // Search input
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                color: const Color(0xFFF6F6F6), // Neutral 100
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.search,
                    size: 24,
                    color: Color(0xFF777777), // Neutral 400
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextField(
                      controller: controller,
                      onChanged: onSearchChanged,
                      decoration: const InputDecoration(
                        hintText: 'Username',
                        hintStyle: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF777777), // Neutral 400
                        ),
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 24,
                    color: const Color(0xFFE5E5E5), // Neutral 200
                  ),
                  const SizedBox(width: 12),
                  GestureDetector(
                    onTap: onQrPressed,
                    child: const Icon(
                      Icons.qr_code_scanner,
                      size: 24,
                      color: Color(0xFF777777), // Neutral 400
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 6),
          // Reload button
          GestureDetector(
            onTap: isReloading ? null : onReloadPressed,
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: const Color(0xFFF6F6F6), // Neutral 100
                borderRadius: BorderRadius.circular(29),
              ),
              child: isReloading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(0xFF777777),
                        ),
                      ),
                    )
                  : const Icon(
                      Icons.refresh,
                      size: 24,
                      color: Color(0xFF777777), // Neutral 400
                    ),
            ),
          ),
          const SizedBox(width: 6),
          // Add button
          GestureDetector(
            onTap: onAddPressed,
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: const Color(0xFFF6F6F6), // Neutral 100
                borderRadius: BorderRadius.circular(29),
              ),
              child: const Icon(
                Icons.add,
                size: 24,
                color: Color(0xFF777777), // Neutral 400
              ),
            ),
          ),
        ],
      ),
    );
  }
}
