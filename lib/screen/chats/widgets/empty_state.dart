import 'package:flutter/material.dart';

/// Empty state widget for when XMTP client is not initialized
class ChatEmptyState extends StatelessWidget {
  final VoidCallback? onInitializePressed;
  final bool isLoading;

  const ChatEmptyState({
    super.key,
    this.onInitializePressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Color(0xFF777777),
          ),
          const SizedBox(height: 16),
          const Text(
            'Welcome to XMTP Chat',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Color(0xFF292929),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Connect your wallet to start messaging',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF777777),
            ),
          ),
          const SizedBox(height: 24),
          if (isLoading)
            const Column(
              children: [
                CircularProgressIndicator(
                  color: Color(0xFF6C4EFF),
                ),
                SizedBox(height: 16),
                Text(
                  'Connecting to XMTP...',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF777777),
                  ),
                ),
              ],
            )
          else
            ElevatedButton(
              onPressed: onInitializePressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6C4EFF),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Connect to XMTP'),
            ),
        ],
      ),
    );
  }
}
