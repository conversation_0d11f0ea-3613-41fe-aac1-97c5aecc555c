import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_state.dart';
import 'package:toii_mesh/core/service/wallet_auth_service.dart';

/// Widget that displays current user's XMTP connection information
class UserInfoWidget extends StatelessWidget {
  const UserInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<XmtpCubit, XmtpState>(
      builder: (context, state) {
        if (state is XmtpConversationsLoaded || state is XmtpClientCreated) {
          return _buildUserInfo(context, state);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildUserInfo(BuildContext context, XmtpState state) {
    final xmtpCubit = context.read<XmtpCubit>();
    final inboxId = xmtpCubit.inboxId;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF0F9FF), // Light blue background
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE0F2FE),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Color(0xFF10B981), // Green dot for connected
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                'XMTP Connected',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF059669),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Wallet Address
          FutureBuilder<String?>(
            future: _getCurrentWalletAddress(),
            builder: (context, snapshot) {
              final walletAddress = snapshot.data;
              if (walletAddress != null) {
                return _buildInfoRow(
                  'Wallet',
                  _truncateAddress(walletAddress),
                  onTap: () => _copyToClipboard(context, walletAddress, 'Wallet address'),
                );
              }
              return const SizedBox.shrink();
            },
          ),

          const SizedBox(height: 8),

          // Inbox ID
          if (inboxId != null)
            _buildInfoRow(
              'Inbox ID',
              _truncateAddress(inboxId),
              onTap: () => _copyToClipboard(context, inboxId, 'Inbox ID'),
              showCopyIcon: true,
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value, {
    VoidCallback? onTap,
    bool showCopyIcon = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            SizedBox(
              width: 80,
              child: Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF6B7280),
                ),
              ),
            ),
            Expanded(
              child: Text(
                value,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF374151),
                  fontFamily: 'monospace',
                ),
              ),
            ),
            if (showCopyIcon)
              const Icon(
                Icons.copy,
                size: 16,
                color: Color(0xFF6B7280),
              ),
          ],
        ),
      ),
    );
  }

  String _truncateAddress(String address) {
    if (address.length <= 16) return address;
    return '${address.substring(0, 8)}...${address.substring(address.length - 8)}';
  }

  Future<String?> _getCurrentWalletAddress() async {
    try {
      final walletAuthService = WalletAuthService();
      final credentials = await walletAuthService.getCurrentWalletCredentials();
      return credentials?.address;
    } catch (e) {
      return null;
    }
  }

  void _copyToClipboard(BuildContext context, String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$label copied to clipboard'),
        duration: const Duration(seconds: 2),
        backgroundColor: const Color(0xFF10B981),
      ),
    );
  }
}
