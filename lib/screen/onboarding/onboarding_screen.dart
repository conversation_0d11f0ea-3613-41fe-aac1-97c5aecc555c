import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/cubit/auth/login/login/login_cubit.dart';
import 'package:toii_mesh/cubit/theme/theme_cubit.dart';
import 'package:toii_mesh/router/app_router.dart';
import 'package:toii_mesh/screen/onboarding/model/onboarding_model.dart';
import 'package:toii_mesh/screen/onboarding/widget/dot_page_indicator.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';
import 'package:toii_mesh/widget/snack_bar/snackbar.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  final _pageController = PageController();

  late List<Widget> _pages;

  late TabController _selectorController;

  int _currentPage = 0;

  Duration get _duration => const Duration(milliseconds: 800);

  @override
  void initState() {
    _pages = listOnboarding.map((e) => _itemOnboarding(e)).toList();

    _selectorController = TabController(length: _pages.length, vsync: this);

    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _selectorController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    _currentPage = index;
    _selectorController.animateTo(index, duration: _duration);
    setState(() {}); // Add setState to rebuild UI when page changes
  }

  void _changePage(int index) {
    if (!_pageController.hasClients) return;

    index == 0
        ? _pageController.jumpToPage(index)
        : _pageController.animateToPage(
            index,
            duration: _duration,
            curve: Curves.ease,
          );
  }

  Widget _itemOnboarding(OnboardingModel item) {
    return Stack(
      children: [
        item.image.image(fit: BoxFit.fitWidth, width: double.infinity),
        Padding(
          padding: const EdgeInsets.only(left: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 91),
              Text(
                item.message,
                style: displayMedium.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 54,
                    color: themeData.black800),
              ),
              const SizedBox(height: 16),
              Text(
                item.description,
                style: titleMedium.copyWith(
                  color: themeData.black800,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(),
      child: Scaffold(
        body: Stack(children: [_carouselSliderWidget(), _nextButton()]),
      ),
    );
  }

  Widget _carouselSliderWidget() {
    return PageView.builder(
      physics: const AlwaysScrollableScrollPhysics(),
      controller: _pageController,
      itemCount: _pages.length,
      onPageChanged: _onPageChanged,
      itemBuilder: (BuildContext context, int index) {
        return _pages[index % _pages.length];
      },
    );
  }

  Widget _nextButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Expanded(child: Container()),
        DotPageIndicator(tabController: _selectorController),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          width: double.infinity,
          height: 82 + MediaQuery.of(context).padding.bottom,
          //     color: themeData.primaryGreen500,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    context.push(RouterEnums.createAcount.routeName);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 56,
                    decoration: BoxDecoration(
                      color: _currentPage == 1
                          ? Colors.white
                          : const Color(0xFF6C4EFF), // Primary blue
                      borderRadius: BorderRadius.circular(48),
                    ),
                    child: Text(
                      'JOIN MESHII',
                      style: TextStyle(
                        fontFamily: 'IBM Plex Sans',
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        height: 1.5,
                        letterSpacing: 0.5,
                        color: _currentPage == 1
                            ? const Color(0xFF6C4EFF) // Primary blue
                            : const Color(0xE6FFFFFF), // White with 90% opacity
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 4),
              BlocConsumer<LoginCubit, LoginState>(
                listener: (context, state) {
                  if (state.status.isSuccess) {
                    context.go(RouterEnums.inital.routeName);
                  }
                  if (state.status.isFailure) {
                    context.showSnackbar(message: state.message ?? "");
                  }
                },
                builder: (context, state) {
                  return Builder(
                    builder: (context) {
                      return GestureDetector(
                        onTap: () {
                          // Handle restore action
                        },
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          height: 60,
                          decoration: BoxDecoration(
                            color: const Color(
                                0xCC000000), // Black with 80% opacity
                            borderRadius: BorderRadius.circular(48),
                          ),
                          child: const Text(
                            'RESTORE',
                            style: TextStyle(
                              fontFamily: 'IBM Plex Sans',
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                              height: 1.5,
                              letterSpacing: 0.5,
                              color:
                                  Color(0xE6FFFFFF), // White with 90% opacity
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
