import 'package:toii_mesh/gen/assets.gen.dart';

final List<OnboardingModel> listOnboarding = [
  OnboardingModel(
    image: Assets.images.image1,
    message: "Welcome to <PERSON><PERSON><PERSON>",
    message1: "",
    description: "100% secure messaging.\n"
        "No data collection.\n"
        "No intrusive ads.\n"
        "You’re in control.",
  ),
  OnboardingModel(
    image: Assets.images.image2,
    message: "",
    message1: "",
    description: "",
  ),
  OnboardingModel(
    image: Assets.images.image3,
    message: "Upgrade to Premium",
    message1: "",
    description: "Unlimited encrypted cloud storage\n"
        "Create groups & private channels\n"
        "Smarter features powered by privacy-first AI",
  ),
];

class OnboardingModel {
  final AssetGenImage image;
  final String message;
  final String message1;
  final String description;
  OnboardingModel({
    required this.image,
    required this.message,
    required this.message1,
    required this.description,
  });
}
