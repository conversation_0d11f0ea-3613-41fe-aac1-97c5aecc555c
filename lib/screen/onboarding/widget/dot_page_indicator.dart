import 'package:flutter/material.dart';

class DotPageIndicator extends AnimatedWidget {
  final TabController tabController;

  const DotPageIndicator({super.key, required this.tabController})
      : super(listenable: tabController);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8, left: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: List.generate(
          tabController.length,
          (index) => AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            margin: const EdgeInsets.only(right: 8),
            height: 8,
            width: tabController.index == index ? 24 : 8,
            decoration: BoxDecoration(
              color: tabController.index == index
                  ? const Color(0xFF6C4EFF)
                  : const Color(0xFF6C4EFF).withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ),
    );
  }
}
