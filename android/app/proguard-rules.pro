# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# Flutter specific rules
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-dontwarn io.flutter.embedding.**

# XMTP specific rules - Keep all XMTP classes
-keep class org.xmtp.** { *; }
-keep interface org.xmtp.** { *; }
-keep enum org.xmtp.** { *; }

# Keep XMTP plugin classes
-keep class com.toii.ai.toii_xmtp_flutter.** { *; }

# Keep protobuf classes used by XMTP
-keep class com.google.protobuf.** { *; }
-keep class * extends com.google.protobuf.GeneratedMessageLite { *; }

# Keep gRPC classes
-keep class io.grpc.** { *; }
-dontwarn io.grpc.**

# Keep crypto and web3j classes
-keep class com.google.crypto.tink.** { *; }
-keep class org.web3j.** { *; }
-dontwarn org.web3j.**

# Keep JNA classes for native library loading
-keep class com.sun.jna.** { *; }
-keep class * implements com.sun.jna.** { *; }
-dontwarn com.sun.jna.**

# Keep Kotlin coroutines
-keep class kotlinx.coroutines.** { *; }
-dontwarn kotlinx.coroutines.**

# Keep BLoC state management classes
-keep class **$State { *; }
-keep class **Cubit { *; }
-keep class **Bloc { *; }
-keep class * extends flutter_bloc.** { *; }

# Keep model classes that might use reflection
-keep class com.example.toii_mesh.model.** { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep classes with custom constructors
-keepclassmembers class * {
    public <init>(...);
}

# Keep enums
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep Serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep annotations
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# Keep Google J2ObjC annotations (referenced by Guava)
-dontwarn com.google.j2objc.annotations.**
-keep class com.google.j2objc.annotations.** { *; }

# Keep Java beans classes (referenced by Jackson)
-dontwarn java.beans.**
-keep class java.beans.** { *; }

# Keep SLF4J logger classes
-dontwarn org.slf4j.**
-keep class org.slf4j.** { *; }
-keep class org.slf4j.impl.** { *; }

# Keep Jackson databind classes
-keep class com.fasterxml.jackson.** { *; }
-dontwarn com.fasterxml.jackson.**

# Keep Guava classes
-keep class com.google.common.** { *; }
-dontwarn com.google.common.**

# Suppress warnings for missing classes that are not used
-dontwarn javax.annotation.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**